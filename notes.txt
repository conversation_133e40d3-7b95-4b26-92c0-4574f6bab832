// test to check printer is working
   Expr expression = new Expr.Binary(
     new Expr.Unary(
         new Token(TokenType.MINUS, "-", null, 1),
         new Expr.Literal(123)),
    new Token(TokenType.STAR, "*", null, 1),
    new Expr.Grouping(
         new Expr.Literal(45.67)));

System.out.println(new AstPrinter().print(expression));


codecrafters test
git add .
git commit -m "commit message"
git push

// to run the program 
./your_program.sh tokenize test.lox 
./your_program.sh parse test.lox 

