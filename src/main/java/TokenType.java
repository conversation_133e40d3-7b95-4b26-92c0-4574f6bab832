enum TokenType {
  // Single-character tokens.
  LEFT_PAREN, RIGHT_PAREN, LEFT_BRACE, RIGHT_BRACE,
  COMMA, DOT, MINUS, PLUS, SEMICOL<PERSON>, SLASH, STAR,

  // One or two character tokens.
  BANG, BA<PERSON>_EQUAL,
  EQUAL, EQUAL_EQUAL,
  G<PERSON><PERSON><PERSON>, G<PERSON><PERSON>ER_EQUAL,
  LESS, LESS_EQUAL,

  // Literals.
  IDENTIFIER, STRING, NUMBER,

  // Keywords.
  AND, CLASS, ELSE, FALSE, FUN, FOR, IF, NIL, OR,
  PRINT, RETURN, SUPER, THIS, TRUE, VAR, WHILE,

  EOF
};