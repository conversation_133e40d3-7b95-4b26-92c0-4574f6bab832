// Test edge cases
print "Edge cases:";

// Large integers
print 999999;
print -123456;

// Division that results in double
print 7 / 2;
print 5 / 4;

// Integer overflow behavior (should work within int range)
print 2147483647;  // Max int value
print -2147483647; // Near min int value (avoiding exact min due to parsing issues)

// Mixed operations preserving types when possible
print 10 + 5;      // int + int = int
print 10 - 5;      // int - int = int  
print 10 * 5;      // int * int = int
print 10 / 5;      // int / int = double (always)

// Unary minus with integers
print -42;
print --42;  // double negative
