# 📌 Features & Ideas Roadmap

This file lists planned features, enhancements, and exploratory ideas for future development. Use it to track priorities and progress.

---

## 🚀 High Priority (Next Up)

---

## 💡 Ideas / Nice to Have
- [ ] copy all the tests of lox language https://github.com/munificent/craftinginterpreters/tree/master/test & create a bash script to execute all of them. maybe good idea would be to create a unit testing package.
- [ ] REPL (READ EXECUTE PRINT LOOP) command and make it fully functional. like jshell
- [ ] support for printing multiple expressions seperated by comma. print "value of 5 + 3 is ", 5+3 ; -- try this at the end of the project.
---

## 🧪 Experimental / Research

---

## ✅ Completed Features

---

_🛠 To suggest new ideas, open an issue or submit a PR to this file._
